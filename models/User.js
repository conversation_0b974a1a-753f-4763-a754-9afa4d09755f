const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
    discordId: {
        type: String,
        required: true,
        unique: true
    },
    minecraftUsername: {
        type: String,
        required: false
    },
    dailyLogin: {
        currentDay: {
            type: Number,
            default: 0
        },
        lastLogin: {
            type: Date,
            default: null
        }
    },
    monthlyStreak: {
        type: Number,
        default: 0
    },
    booster: {
        type: Boolean,
        default: false
    },
    totalSpins: {
        type: Number,
        default: 0
    },
    totalWins: {
        type: Number,
        default: 0
    },
    lastSpin: {
        type: Date,
        default: null
    },
    rewards: {
        common: { type: Number, default: 0 },
        rare: { type: Number, default: 0 },
        epic: { type: Number, default: 0 },
        legendary: { type: Number, default: 0 }
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// Update the updatedAt field before saving
userSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

// Static method to find or create user
userSchema.statics.findOrCreate = async function(discordId) {
    let user = await this.findOne({ discordId });
    if (!user) {
        user = new this({ discordId });
        await user.save();
    }
    return user;
};

module.exports = mongoose.model('User', userSchema);
