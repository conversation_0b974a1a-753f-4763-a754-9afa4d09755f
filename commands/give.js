const { SlashCommandBuilder } = require('discord.js');
const User = require('../models/User');
const AdvancedEmbedBuilder = require('../utils/createAdvancedEmbed');
const rconHandler = require('../utils/rconHandler');
const commandHandler = require('../utils/commandHandler');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('give')
        .setDescription('Give rewards to a user (Owner only)')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The Discord user to give rewards to')
                .setRequired(true)
        )
        .addStringOption(option =>
            option.setName('reward')
                .setDescription('Type of reward to give')
                .setRequired(true)
                .addChoices(
                    { name: 'Common Chest', value: 'common' },
                    { name: 'Rare Chest', value: 'rare' },
                    { name: 'Epic Chest', value: 'epic' },
                    { name: 'Legendary Jackpot', value: 'legendary' },
                    { name: 'Daily Reward Day 1', value: 'daily_1' },
                    { name: 'Daily Reward Day 2', value: 'daily_2' },
                    { name: 'Daily Reward Day 3', value: 'daily_3' },
                    { name: 'Daily Reward Day 4', value: 'daily_4' },
                    { name: 'Daily Reward Day 5', value: 'daily_5' },
                    { name: 'Daily Reward Day 6', value: 'daily_6' },
                    { name: 'Daily Reward Day 7 (Mega)', value: 'daily_7' },
                    { name: 'Custom Item', value: 'custom' }
                )
        )
        .addStringOption(option =>
            option.setName('custom_item')
                .setDescription('Custom item to give (only if reward type is "custom")')
                .setRequired(false)
        )
        .addIntegerOption(option =>
            option.setName('amount')
                .setDescription('Amount of the custom item (default: 1)')
                .setRequired(false)
                .setMinValue(1)
                .setMaxValue(64)
        )
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for giving the reward (optional)')
                .setRequired(false)
        ),
    category: 'Admin',

    async execute(interaction) {
        try {
            // Log command usage
            commandHandler.logCommandUsage(interaction, 'give');

            // Check if user is the bot owner
            if (!commandHandler.hasPermission(interaction, 'owner')) {
                return await commandHandler.sendPermissionDenied(interaction, 'owner');
            }

            const targetUser = interaction.options.getUser('user');
            const rewardType = interaction.options.getString('reward');
            const customItem = interaction.options.getString('custom_item');
            const amount = interaction.options.getInteger('amount') || 1;
            const reason = interaction.options.getString('reason');

            // Find the target user in database
            const dbUser = await User.findOne({ discordId: targetUser.id });
            
            if (!dbUser || !dbUser.minecraftUsername) {
                const errorEmbed = AdvancedEmbedBuilder.createErrorEmbed(
                    'User Not Found',
                    `${targetUser.displayName || targetUser.username} doesn't have a linked Minecraft account.`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Defer reply for processing
            await interaction.deferReply();

            let rewardDescription = '';
            let success = false;

            try {
                if (rewardType === 'custom') {
                    if (!customItem) {
                        const errorEmbed = AdvancedEmbedBuilder.createErrorEmbed(
                            'Missing Custom Item',
                            'You must specify a custom item when using the "custom" reward type.'
                        );
                        return await interaction.editReply({ embeds: [errorEmbed] });
                    }

                    // Give custom item
                    await rconHandler.giveItem(dbUser.minecraftUsername, `${customItem} ${amount}`);
                    rewardDescription = `${amount}x ${customItem}`;
                    success = true;

                } else if (rewardType.startsWith('daily_')) {
                    // Give daily reward
                    const day = parseInt(rewardType.split('_')[1]);
                    success = await rconHandler.giveDailyReward(dbUser.minecraftUsername, day);
                    rewardDescription = `Daily Reward Day ${day}`;

                } else {
                    // Give slot machine reward
                    success = await rconHandler.giveReward(dbUser.minecraftUsername, rewardType);
                    rewardDescription = `${rewardType.charAt(0).toUpperCase() + rewardType.slice(1)} Chest`;
                }

                if (success) {
                    // Update user statistics if it's a slot machine reward
                    if (['common', 'rare', 'epic', 'legendary'].includes(rewardType)) {
                        dbUser.rewards[rewardType] = (dbUser.rewards[rewardType] || 0) + 1;
                        dbUser.totalWins = (dbUser.totalWins || 0) + 1;
                        await dbUser.save();
                    }

                    // Create success embed
                    const successEmbed = AdvancedEmbedBuilder.createSuccessEmbed(
                        'Reward Given Successfully',
                        `Successfully gave **${rewardDescription}** to **${dbUser.minecraftUsername}**.`,
                        {
                            author: {
                                name: targetUser.displayName || targetUser.username,
                                iconURL: targetUser.displayAvatarURL()
                            },
                            thumbnail: `https://mc-heads.net/avatar/${dbUser.minecraftUsername}/100`
                        }
                    );

                    successEmbed.addFields(
                        {
                            name: '👤 Recipient',
                            value: [
                                `**Discord:** ${targetUser.tag}`,
                                `**Minecraft:** ${dbUser.minecraftUsername}`
                            ].join('\n'),
                            inline: true
                        },
                        {
                            name: '🎁 Reward Details',
                            value: [
                                `**Type:** ${rewardDescription}`,
                                `**Given by:** ${interaction.user.tag}`,
                                `**Time:** <t:${Math.floor(Date.now() / 1000)}:F>`
                            ].join('\n'),
                            inline: true
                        }
                    );

                    if (reason) {
                        successEmbed.addFields({
                            name: '📝 Reason',
                            value: reason,
                            inline: false
                        });
                    }

                    await interaction.editReply({ embeds: [successEmbed] });

                    // Send notification to the recipient (if they're in the server)
                    try {
                        const guild = interaction.guild;
                        const member = await guild.members.fetch(targetUser.id);
                        
                        if (member) {
                            const notificationEmbed = AdvancedEmbedBuilder.createSuccessEmbed(
                                'You Received a Reward!',
                                `You have been given **${rewardDescription}** by the bot owner!`,
                                {
                                    thumbnail: `https://mc-heads.net/avatar/${dbUser.minecraftUsername}/100`
                                }
                            );

                            if (reason) {
                                notificationEmbed.addFields({
                                    name: '📝 Reason',
                                    value: reason,
                                    inline: false
                                });
                            }

                            notificationEmbed.addFields({
                                name: '🎮 Check Your Minecraft Account',
                                value: 'The reward has been added to your Minecraft inventory!',
                                inline: false
                            });

                            await targetUser.send({ embeds: [notificationEmbed] }).catch(() => {
                                // User has DMs disabled, that's okay
                                console.log(`📝 Could not send DM notification to ${targetUser.tag}`);
                            });
                        }
                    } catch (error) {
                        // User not in server or other error, continue anyway
                        console.log(`📝 Could not send notification to ${targetUser.tag}:`, error.message);
                    }

                    console.log(`🎁 Owner reward: ${interaction.user.tag} gave ${rewardDescription} to ${dbUser.minecraftUsername} (${targetUser.tag})`);

                } else {
                    throw new Error('Failed to give reward via RCON');
                }

            } catch (rconError) {
                console.error('❌ RCON error during owner reward:', rconError);
                
                const errorEmbed = AdvancedEmbedBuilder.createErrorEmbed(
                    'Reward Failed',
                    'Failed to give the reward. The Minecraft server might be offline or there was an RCON error.'
                );
                
                errorEmbed.addFields({
                    name: '🔧 Technical Details',
                    value: `\`\`\`${rconError.message}\`\`\``,
                    inline: false
                });

                await interaction.editReply({ embeds: [errorEmbed] });
            }

        } catch (error) {
            console.error('❌ Error in give command:', error);
            
            try {
                if (interaction.deferred) {
                    await interaction.editReply({
                        embeds: [AdvancedEmbedBuilder.createErrorEmbed(
                            'Command Error',
                            'An error occurred while processing the give command.'
                        )]
                    });
                } else {
                    await commandHandler.handleCommandError(interaction, error, 'give');
                }
            } catch (replyError) {
                console.error('❌ Error sending error response:', replyError);
            }
        }
    }
};
