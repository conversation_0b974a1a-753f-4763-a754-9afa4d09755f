const { SlashCommandBuilder } = require('discord.js');
const User = require('../models/User');
const AdvancedEmbedBuilder = require('../utils/createAdvancedEmbed');
const rconHandler = require('../utils/rconHandler');
const commandHandler = require('../utils/commandHandler');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('login')
        .setDescription('Claim your daily login reward!'),
    category: 'Rewards',

    async execute(interaction) {
        try {
            // Log command usage
            commandHandler.logCommandUsage(interaction, 'login');

            const discordId = interaction.user.id;

            // Find user and check if they have a linked Minecraft account
            const user = await User.findOne({ discordId });
            
            if (!user || !user.minecraftUsername) {
                const errorEmbed = AdvancedEmbedBuilder.createErrorEmbed(
                    'Account Not Linked',
                    'You need to link your Minecraft account first before you can claim daily rewards.\n\nUse `/link <username>` to link your account.'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const lastLogin = user.dailyLogin.lastLogin;
            
            // Check if user has already claimed today's reward
            if (lastLogin) {
                const lastLoginDate = new Date(lastLogin.getFullYear(), lastLogin.getMonth(), lastLogin.getDate());
                
                if (lastLoginDate.getTime() === today.getTime()) {
                    const alreadyClaimedEmbed = AdvancedEmbedBuilder.createWarningEmbed(
                        'Already Claimed',
                        'You have already claimed your daily reward today!\n\nCome back tomorrow for your next reward.'
                    );
                    
                    const nextRewardTime = new Date(today);
                    nextRewardTime.setDate(nextRewardTime.getDate() + 1);
                    const nextRewardTimestamp = Math.floor(nextRewardTime.getTime() / 1000);
                    
                    alreadyClaimedEmbed.addFields({
                        name: '⏰ Next Reward Available',
                        value: `<t:${nextRewardTimestamp}:R>`,
                        inline: false
                    });

                    return await interaction.reply({ embeds: [alreadyClaimedEmbed], ephemeral: true });
                }
            }

            // Defer reply for processing
            await interaction.deferReply();

            // Calculate current day in streak
            let currentDay = user.dailyLogin.currentDay || 0;
            
            // Check if streak should continue or reset
            if (lastLogin) {
                const yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);
                const lastLoginDate = new Date(lastLogin.getFullYear(), lastLogin.getMonth(), lastLogin.getDate());
                
                if (lastLoginDate.getTime() === yesterday.getTime()) {
                    // Consecutive day - continue streak
                    currentDay = (currentDay % 7) + 1;
                } else {
                    // Streak broken - reset to day 1
                    currentDay = 1;
                }
            } else {
                // First time login
                currentDay = 1;
            }

            // Define daily rewards
            const dailyRewards = {
                1: {
                    name: 'Starter',
                    description: '• 5x Cooked Beef\n• 5x Bread',
                    emoji: '🥖'
                },
                2: {
                    name: 'Defender',
                    description: '• 1x Shield\n• 10x Arrows',
                    emoji: '🛡️'
                },
                3: {
                    name: 'Tool Set',
                    description: '• 1x Iron Axe\n• 5x Iron Ingots',
                    emoji: '⛏️'
                },
                4: {
                    name: 'Ranger',
                    description: '• 1x Bow (Power I)\n• 3x XP Bottles',
                    emoji: '🏹'
                },
                5: {
                    name: 'Alchemist',
                    description: '• 1x Random Potion\n  (Healing, Strength, or Fire Resistance)',
                    emoji: '🧪'
                },
                6: {
                    name: 'Survivor',
                    description: '• 1x Totem of Undying\n• 20x XP Bottles',
                    emoji: '🪬'
                },
                7: {
                    name: 'Mega Chest - Exalted',
                    description: '• 10x Diamonds\n• 64x XP Bottles\n• 3x Premium Potions\n• 2x Enchanted Golden Apples\n• 1x Netherite Ingot\n• 10% chance for extra Totem!',
                    emoji: '💎'
                }
            };

            const todayReward = dailyRewards[currentDay];

            // Give reward via RCON
            try {
                const rewardSuccess = await rconHandler.giveDailyReward(user.minecraftUsername, currentDay);
                
                if (!rewardSuccess) {
                    console.warn(`⚠️ Failed to give daily reward to ${user.minecraftUsername} via RCON`);
                }
            } catch (rconError) {
                console.error('❌ RCON error during daily reward distribution:', rconError);
                // Continue with Discord response even if RCON fails
            }

            // Update user data
            user.dailyLogin.currentDay = currentDay;
            user.dailyLogin.lastLogin = now;
            user.monthlyStreak = (user.monthlyStreak || 0) + 1;
            await user.save();

            // Create reward embed
            const rewardEmbed = AdvancedEmbedBuilder.createDailyLoginEmbed(
                currentDay,
                todayReward,
                interaction.user
            );

            // Add streak information
            rewardEmbed.addFields(
                {
                    name: '🔥 Login Streak',
                    value: `Day ${currentDay} of 7`,
                    inline: true
                },
                {
                    name: '📅 Monthly Progress',
                    value: `${user.monthlyStreak} days this month`,
                    inline: true
                }
            );

            // Add progress bar for the week
            const progressBar = this.createProgressBar(currentDay, 7);
            rewardEmbed.addFields({
                name: '📊 Weekly Progress',
                value: progressBar,
                inline: false
            });

            // Add special message for day 7
            if (currentDay === 7) {
                rewardEmbed.addFields({
                    name: '🎉 Congratulations!',
                    value: 'You\'ve completed a full week of daily logins! Your streak will reset to Day 1 tomorrow.',
                    inline: false
                });
            }

            // Add next reward preview
            const nextDay = currentDay === 7 ? 1 : currentDay + 1;
            const nextReward = dailyRewards[nextDay];
            rewardEmbed.addFields({
                name: `🔮 Tomorrow's Reward (Day ${nextDay})`,
                value: `${nextReward.emoji} **${nextReward.name}**\n${nextReward.description}`,
                inline: false
            });

            await interaction.editReply({ embeds: [rewardEmbed] });

            console.log(`🎁 Daily login: ${interaction.user.tag} (${user.minecraftUsername}) claimed Day ${currentDay} reward`);

        } catch (error) {
            console.error('❌ Error in login command:', error);
            
            // Try to respond with error if not already replied
            try {
                if (interaction.deferred) {
                    await interaction.editReply({
                        embeds: [AdvancedEmbedBuilder.createErrorEmbed(
                            'Daily Login Error',
                            'An error occurred while claiming your daily reward. Please try again later.'
                        )]
                    });
                } else {
                    await commandHandler.handleCommandError(interaction, error, 'login');
                }
            } catch (replyError) {
                console.error('❌ Error sending error response:', replyError);
            }
        }
    },

    /**
     * Create a visual progress bar
     * @param {number} current - Current progress
     * @param {number} total - Total steps
     * @returns {string} - Progress bar string
     */
    createProgressBar(current, total) {
        const filled = '🟩';
        const empty = '⬜';
        const progress = Math.min(current, total);
        
        let bar = '';
        for (let i = 1; i <= total; i++) {
            bar += i <= progress ? filled : empty;
        }
        
        return `${bar} ${current}/${total}`;
    }
};
