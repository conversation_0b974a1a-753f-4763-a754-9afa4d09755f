const { SlashCommandBuilder } = require('discord.js');
const User = require('../models/User');
const AdvancedEmbedBuilder = require('../utils/createAdvancedEmbed');
const rconHandler = require('../utils/rconHandler');
const commandHandler = require('../utils/commandHandler');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('spin')
        .setDescription('Play the slot machine for a chance to win rewards!'),
    category: 'Rewards',

    async execute(interaction) {
        try {
            // Log command usage
            commandHandler.logCommandUsage(interaction, 'spin');

            const discordId = interaction.user.id;

            // Find user and check if they have a linked Minecraft account
            const user = await User.findOne({ discordId });
            
            if (!user || !user.minecraftUsername) {
                const errorEmbed = AdvancedEmbedBuilder.createErrorEmbed(
                    'Account Not Linked',
                    'You need to link your Minecraft account first before you can use the slot machine.\n\nUse `/link <username>` to link your account.'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Check cooldown (optional - you can implement a cooldown system)
            const now = new Date();
            const lastSpin = user.lastSpin;
            const cooldownTime = 5 * 60 * 1000; // 5 minutes cooldown

            if (lastSpin && (now - lastSpin) < cooldownTime) {
                const remainingTime = Math.ceil((cooldownTime - (now - lastSpin)) / 1000 / 60);
                const cooldownEmbed = AdvancedEmbedBuilder.createWarningEmbed(
                    'Cooldown Active',
                    `You can spin again in **${remainingTime}** minute(s).`
                );
                return await interaction.reply({ embeds: [cooldownEmbed], ephemeral: true });
            }

            // Defer reply for processing
            await interaction.deferReply();

            // Slot machine logic with defined probabilities
            const random = Math.random() * 100;
            let result, rewardType, rewardDescription;

            if (random <= 1) { // 1% chance
                result = 'legendary';
                rewardType = 'Legendary Jackpot';
                rewardDescription = [
                    '🟨 **LEGENDARY JACKPOT!** 🟨',
                    '',
                    '🔥 **Premium Package:**',
                    '• 1x Beacon',
                    '• 1x Enchanted Diamond Sword (Sharpness IV)',
                    '• Full Diamond Armor Set (Protection IV)',
                    '• 1x Elytra (Unbreaking III)',
                    '• 200x XP Bottles',
                    '',
                    '✨ *The ultimate prize!*'
                ].join('\n');
            } else if (random <= 10) { // 9% chance (10% - 1%)
                result = 'epic';
                rewardType = 'Epic Chest';
                rewardDescription = [
                    '🟪 **EPIC REWARD!** 🟪',
                    '',
                    '⚔️ **Epic Package:**',
                    '• 1x Enchanted Diamond Sword',
                    '  (Sharpness III, Unbreaking II)',
                    '• 50x XP Bottles',
                    '• 1x Potion of Swiftness (3 min)',
                    '',
                    '🎉 *Excellent luck!*'
                ].join('\n');
            } else if (random <= 40) { // 30% chance (40% - 10%)
                result = 'rare';
                rewardType = 'Rare Chest';
                rewardDescription = [
                    '🟫 **RARE REWARD!** 🟫',
                    '',
                    '💎 **Rare Package:**',
                    '• 1x Diamond Block',
                    '• 30x XP Bottles',
                    '• 1x Enchanted Book (Low-level)',
                    '',
                    '😊 *Nice find!*'
                ].join('\n');
            } else { // 60% chance (100% - 40%)
                result = 'common';
                rewardType = 'Common Chest';
                rewardDescription = [
                    '🟦 **COMMON REWARD** 🟦',
                    '',
                    '📦 **Common Package:**',
                    '• 20x XP Bottles',
                    '• 10x Arrows',
                    '• 5x Cooked Beef',
                    '',
                    '👍 *Better luck next time!*'
                ].join('\n');
            }

            // Update user statistics
            user.totalSpins = (user.totalSpins || 0) + 1;
            user.totalWins = (user.totalWins || 0) + 1;
            user.lastSpin = now;
            user.rewards[result] = (user.rewards[result] || 0) + 1;
            await user.save();

            // Give reward via RCON
            try {
                const rewardSuccess = await rconHandler.giveReward(user.minecraftUsername, result);
                
                if (!rewardSuccess) {
                    console.warn(`⚠️ Failed to give ${result} reward to ${user.minecraftUsername} via RCON`);
                }
            } catch (rconError) {
                console.error('❌ RCON error during reward distribution:', rconError);
                // Continue with Discord response even if RCON fails
            }

            // Create result embed
            const resultEmbed = AdvancedEmbedBuilder.createSlotMachineEmbed(result, { description: rewardDescription }, interaction.user);

            // Add statistics
            resultEmbed.addFields(
                {
                    name: '📊 Your Statistics',
                    value: [
                        `🎰 Total Spins: ${user.totalSpins}`,
                        `🏆 Total Wins: ${user.totalWins}`,
                        `🎯 Win Rate: ${((user.totalWins / user.totalSpins) * 100).toFixed(1)}%`
                    ].join('\n'),
                    inline: true
                },
                {
                    name: '🎁 Reward Breakdown',
                    value: [
                        `🟨 Legendary: ${user.rewards.legendary || 0}`,
                        `🟪 Epic: ${user.rewards.epic || 0}`,
                        `🟫 Rare: ${user.rewards.rare || 0}`,
                        `🟦 Common: ${user.rewards.common || 0}`
                    ].join('\n'),
                    inline: true
                }
            );

            // Add cooldown info
            const nextSpinTime = Math.floor((now.getTime() + cooldownTime) / 1000);
            resultEmbed.addFields({
                name: '⏰ Next Spin Available',
                value: `<t:${nextSpinTime}:R>`,
                inline: false
            });

            await interaction.editReply({ embeds: [resultEmbed] });

            console.log(`🎰 Slot machine: ${interaction.user.tag} (${user.minecraftUsername}) won ${result}`);

        } catch (error) {
            console.error('❌ Error in spin command:', error);
            
            // Try to respond with error if not already replied
            try {
                if (interaction.deferred) {
                    await interaction.editReply({
                        embeds: [AdvancedEmbedBuilder.createErrorEmbed(
                            'Slot Machine Error',
                            'An error occurred while spinning. Please try again later.'
                        )]
                    });
                } else {
                    await commandHandler.handleCommandError(interaction, error, 'spin');
                }
            } catch (replyError) {
                console.error('❌ Error sending error response:', replyError);
            }
        }
    }
};
