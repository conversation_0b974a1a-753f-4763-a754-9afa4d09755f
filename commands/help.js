const { <PERSON><PERSON><PERSON><PERSON>mandBuilder, ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');
const AdvancedEmbedBuilder = require('../utils/createAdvancedEmbed');
const commandHandler = require('../utils/commandHandler');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('help')
        .setDescription('Get help with bot commands and features')
        .addStringOption(option =>
            option.setName('command')
                .setDescription('Get detailed help for a specific command')
                .setRequired(false)
        ),
    category: 'General',

    async execute(interaction) {
        try {
            // Log command usage
            commandHandler.logCommandUsage(interaction, 'help');

            const specificCommand = interaction.options.getString('command');

            if (specificCommand) {
                // Show help for specific command
                await this.showCommandHelp(interaction, specificCommand);
            } else {
                // Show general help with interactive menu
                await this.showGeneralHelp(interaction);
            }

        } catch (error) {
            console.error('❌ Error in help command:', error);
            await commandHandler.handleCommandError(interaction, error, 'help');
        }
    },

    async showGeneralHelp(interaction) {
        const embed = AdvancedEmbedBuilder.createBasicEmbed({
            title: '📚 The Basement Bot - Help Center',
            description: 'Welcome to The Basement Bot! Here\'s everything you need to know to get started.',
            color: 0x00AE86,
            thumbnail: interaction.client.user.displayAvatarURL()
        });

        embed.addFields(
            {
                name: '🚀 Getting Started',
                value: [
                    '1. **Link your account** with `/link <username>`',
                    '2. **Claim daily rewards** with `/login`',
                    '3. **Play the slot machine** with `/spin`',
                    '4. **Chat in Minecraft** through Discord!'
                ].join('\n'),
                inline: false
            },
            {
                name: '🎮 Core Features',
                value: [
                    '🔗 **Account Linking** - Connect Discord to Minecraft',
                    '🎰 **Slot Machine** - Win amazing rewards',
                    '🎁 **Daily Rewards** - 7-day login cycle',
                    '💬 **Chat Bridge** - Real-time chat between platforms',
                    '🏆 **Monthly Rewards** - Premium rewards for active players'
                ].join('\n'),
                inline: false
            },
            {
                name: '📋 Command Categories',
                value: [
                    '**General Commands** - Basic bot functions',
                    '**Reward Commands** - Earn and claim rewards',
                    '**Admin Commands** - Server management (restricted)'
                ].join('\n'),
                inline: false
            }
        );

        // Create interactive menu
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('help_category')
            .setPlaceholder('Select a category for detailed help')
            .addOptions([
                {
                    label: 'General Commands',
                    description: 'Basic commands available to everyone',
                    value: 'general',
                    emoji: '📝'
                },
                {
                    label: 'Reward Commands',
                    description: 'Commands for earning and claiming rewards',
                    value: 'rewards',
                    emoji: '🎁'
                },
                {
                    label: 'Admin Commands',
                    description: 'Administrative commands (restricted)',
                    value: 'admin',
                    emoji: '⚙️'
                },
                {
                    label: 'Slot Machine Guide',
                    description: 'Learn about the slot machine rewards',
                    value: 'slots',
                    emoji: '🎰'
                },
                {
                    label: 'Daily Rewards Guide',
                    description: 'Learn about the daily login system',
                    value: 'daily',
                    emoji: '📅'
                }
            ]);

        const row = new ActionRowBuilder().addComponents(selectMenu);

        embed.addFields({
            name: '💡 Need More Help?',
            value: 'Use the dropdown menu below to explore specific topics, or use `/help <command>` for detailed command information.',
            inline: false
        });

        await interaction.reply({ embeds: [embed], components: [row] });
    },

    async showCommandHelp(interaction, commandName) {
        const commandHelp = {
            'link': {
                description: 'Link your Discord account to your Minecraft username',
                usage: '/link <username>',
                examples: ['/link Steve', '/link Notch'],
                notes: [
                    'Username must be 3-16 characters',
                    'Only letters, numbers, and underscores allowed',
                    'Each Minecraft username can only be linked to one Discord account'
                ]
            },
            'spin': {
                description: 'Play the slot machine for a chance to win rewards',
                usage: '/spin',
                examples: ['/spin'],
                notes: [
                    'Requires linked Minecraft account',
                    '5-minute cooldown between spins',
                    'Rewards are automatically given to your Minecraft account'
                ]
            },
            'login': {
                description: 'Claim your daily login reward',
                usage: '/login',
                examples: ['/login'],
                notes: [
                    'Requires linked Minecraft account',
                    'Can only claim once per day',
                    '7-day cycle with special Day 7 mega reward'
                ]
            },
            'help': {
                description: 'Get help with bot commands and features',
                usage: '/help [command]',
                examples: ['/help', '/help spin', '/help login'],
                notes: [
                    'Use without arguments for general help',
                    'Specify a command name for detailed help'
                ]
            }
        };

        const helpData = commandHelp[commandName.toLowerCase()];
        
        if (!helpData) {
            const errorEmbed = AdvancedEmbedBuilder.createErrorEmbed(
                'Command Not Found',
                `No help available for command \`${commandName}\`. Use \`/help\` to see all available commands.`
            );
            return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
        }

        const embed = AdvancedEmbedBuilder.createBasicEmbed({
            title: `📖 Help: /${commandName}`,
            description: helpData.description,
            color: 0x0099FF
        });

        embed.addFields(
            {
                name: '📝 Usage',
                value: `\`${helpData.usage}\``,
                inline: false
            },
            {
                name: '💡 Examples',
                value: helpData.examples.map(ex => `\`${ex}\``).join('\n'),
                inline: false
            }
        );

        if (helpData.notes && helpData.notes.length > 0) {
            embed.addFields({
                name: '📋 Important Notes',
                value: helpData.notes.map(note => `• ${note}`).join('\n'),
                inline: false
            });
        }

        await interaction.reply({ embeds: [embed], ephemeral: true });
    }
};

// Handle select menu interactions (this would typically be in a separate interaction handler)
// For now, we'll include it here as a reference
/*
client.on('interactionCreate', async (interaction) => {
    if (!interaction.isStringSelectMenu()) return;
    
    if (interaction.customId === 'help_category') {
        const category = interaction.values[0];
        
        // Handle different help categories
        switch (category) {
            case 'general':
                // Show general commands help
                break;
            case 'rewards':
                // Show reward commands help
                break;
            case 'admin':
                // Show admin commands help
                break;
            case 'slots':
                // Show slot machine guide
                break;
            case 'daily':
                // Show daily rewards guide
                break;
        }
    }
});
*/
