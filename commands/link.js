const { SlashCommandBuilder } = require('discord.js');
const User = require('../models/User');
const AdvancedEmbedBuilder = require('../utils/createAdvancedEmbed');
const commandHandler = require('../utils/commandHandler');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('link')
        .setDescription('Link your Discord account to your Minecraft username')
        .addStringOption(option =>
            option.setName('username')
                .setDescription('Your Minecraft username')
                .setRequired(true)
                .setMaxLength(16)
                .setMinLength(3)
        ),
    category: 'General',

    async execute(interaction) {
        try {
            // Log command usage
            commandHandler.logCommandUsage(interaction, 'link');

            const minecraftUsername = interaction.options.getString('username');
            const discordId = interaction.user.id;

            // Validate Minecraft username format (basic validation)
            const usernameRegex = /^[a-zA-Z0-9_]{3,16}$/;
            if (!usernameRegex.test(minecraftUsername)) {
                const errorEmbed = AdvancedEmbedBuilder.createErrorEmbed(
                    'Invalid Username',
                    'Please provide a valid Minecraft username (3-16 characters, letters, numbers, and underscores only).'
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Check if this Minecraft username is already linked to another Discord account
            const existingUser = await User.findOne({ 
                minecraftUsername: minecraftUsername,
                discordId: { $ne: discordId }
            });

            if (existingUser) {
                const errorEmbed = AdvancedEmbedBuilder.createErrorEmbed(
                    'Username Already Linked',
                    `The Minecraft username **${minecraftUsername}** is already linked to another Discord account.`
                );
                return await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }

            // Find or create user record
            let user = await User.findOne({ discordId });
            
            if (!user) {
                // Create new user
                user = new User({
                    discordId,
                    minecraftUsername
                });
            } else {
                // Update existing user
                const oldUsername = user.minecraftUsername;
                user.minecraftUsername = minecraftUsername;
                
                if (oldUsername && oldUsername !== minecraftUsername) {
                    // Username changed
                    const changeEmbed = AdvancedEmbedBuilder.createSuccessEmbed(
                        'Account Updated',
                        `Your linked Minecraft account has been updated from **${oldUsername}** to **${minecraftUsername}**.`,
                        {
                            author: {
                                name: interaction.user.displayName || interaction.user.username,
                                iconURL: interaction.user.displayAvatarURL()
                            }
                        }
                    );

                    changeEmbed.addFields(
                        {
                            name: 'Previous Username',
                            value: oldUsername,
                            inline: true
                        },
                        {
                            name: 'New Username',
                            value: minecraftUsername,
                            inline: true
                        }
                    );

                    await user.save();
                    return await interaction.reply({ embeds: [changeEmbed], ephemeral: true });
                }
            }

            await user.save();

            // Create success embed
            const successEmbed = AdvancedEmbedBuilder.createSuccessEmbed(
                'Account Linked Successfully',
                `Your Discord account has been linked to the Minecraft username **${minecraftUsername}**.`,
                {
                    author: {
                        name: interaction.user.displayName || interaction.user.username,
                        iconURL: interaction.user.displayAvatarURL()
                    },
                    thumbnail: `https://mc-heads.net/avatar/${minecraftUsername}/100`
                }
            );

            successEmbed.addFields(
                {
                    name: 'What\'s Next?',
                    value: [
                        '🎰 Use `/spin` to play the slot machine',
                        '🎁 Use `/login` to claim daily rewards',
                        '💬 Chat in <#' + (process.env.MC_CHAT_CHANNEL_ID || 'mc-chat') + '> to bridge to Minecraft',
                        '📚 Use `/help` to see all available commands'
                    ].join('\n'),
                    inline: false
                },
                {
                    name: 'Account Info',
                    value: [
                        `**Discord:** ${interaction.user.tag}`,
                        `**Minecraft:** ${minecraftUsername}`,
                        `**Linked:** <t:${Math.floor(Date.now() / 1000)}:R>`
                    ].join('\n'),
                    inline: false
                }
            );

            await interaction.reply({ embeds: [successEmbed], ephemeral: true });

            console.log(`✅ Account linked: ${interaction.user.tag} → ${minecraftUsername}`);

        } catch (error) {
            console.error('❌ Error in link command:', error);
            await commandHandler.handleCommandError(interaction, error, 'link');
        }
    }
};
