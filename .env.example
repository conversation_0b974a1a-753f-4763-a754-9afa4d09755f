# Discord Bot Configuration
BOT_TOKEN=your_discord_bot_token_here
CLIENT_ID=your_discord_application_client_id

# MongoDB Configuration
MONGO_URI=mongodb://localhost:27017/basement-bot

# Minecraft RCON Configuration
RCON_HOST=your.minecraft.server.ip
RCON_PORT=25575
RCON_PASSWORD=your_rcon_password

# Minecraft Server Configuration
MC_LOG_PATH=/path/to/minecraft/server/logs/latest.log
MC_CHAT_CHANNEL_ID=your_discord_channel_id_for_mc_chat

# Bot Configuration
OWNER_ID=616869732347543555
ADMIN_ROLE_ID=your_admin_role_id
MODERATOR_ROLE_ID=your_moderator_role_id

# Slot Machine Configuration
SLOT_MACHINE_GIF_URL=https://example.com/slot-machine.gif

# Optional: Guild ID for faster command registration during development
GUILD_ID=your_discord_server_id
