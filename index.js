require('dotenv').config();
const { Client, GatewayIntentBits, Collection, Events } = require('discord.js');
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const cron = require('node-cron');

// Import utilities
const commandHandler = require('./utils/commandHandler');
const mcChatWatcher = require('./utils/mcChatWatcher');
const mcChatBridge = require('./utils/mcChatBridge');
const rewardMonthly = require('./utils/rewardMonthly');

// Create Discord client with necessary intents
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMembers
    ]
});

// Initialize commands collection
client.commands = new Collection();

// Load commands
const commandsPath = path.join(__dirname, 'commands');
if (fs.existsSync(commandsPath)) {
    const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));
    
    for (const file of commandFiles) {
        const filePath = path.join(commandsPath, file);
        const command = require(filePath);
        
        if ('data' in command && 'execute' in command) {
            client.commands.set(command.data.name, command);
            console.log(`✅ Loaded command: ${command.data.name}`);
        } else {
            console.log(`⚠️  Command at ${filePath} is missing required "data" or "execute" property.`);
        }
    }
}

// Bot ready event
client.once(Events.ClientReady, async (readyClient) => {
    console.log(`🚀 Bot is ready! Logged in as ${readyClient.user.tag}`);
    
    // Register slash commands
    try {
        await commandHandler.registerCommands(client);
        console.log('✅ Slash commands registered successfully');
    } catch (error) {
        console.error('❌ Error registering slash commands:', error);
    }
    
    // Initialize Minecraft chat watcher
    try {
        mcChatWatcher.initialize(client);
        console.log('✅ Minecraft chat watcher initialized');
    } catch (error) {
        console.error('❌ Error initializing chat watcher:', error);
    }
    
    // Set bot status
    client.user.setActivity('Minecraft Server | /help', { type: 'WATCHING' });
});

// Handle slash command interactions
client.on(Events.InteractionCreate, async (interaction) => {
    if (!interaction.isChatInputCommand()) return;
    
    const command = client.commands.get(interaction.commandName);
    
    if (!command) {
        console.error(`❌ No command matching ${interaction.commandName} was found.`);
        return;
    }
    
    try {
        await command.execute(interaction);
    } catch (error) {
        console.error(`❌ Error executing command ${interaction.commandName}:`, error);
        
        const errorMessage = {
            content: '❌ There was an error while executing this command!',
            ephemeral: true
        };
        
        if (interaction.replied || interaction.deferred) {
            await interaction.followUp(errorMessage);
        } else {
            await interaction.reply(errorMessage);
        }
    }
});

// Handle Discord to Minecraft chat bridge
client.on(Events.MessageCreate, async (message) => {
    // Skip bot messages and messages not in designated channel
    if (message.author.bot) return;
    
    // Check if message is in mc-chat channel (you can configure this)
    if (message.channel.name === 'mc-chat' || message.channel.id === process.env.MC_CHAT_CHANNEL_ID) {
        try {
            await mcChatBridge.sendToMinecraft(message);
        } catch (error) {
            console.error('❌ Error bridging message to Minecraft:', error);
        }
    }
});

// Error handling
client.on(Events.Error, (error) => {
    console.error('❌ Discord client error:', error);
});

process.on('unhandledRejection', (error) => {
    console.error('❌ Unhandled promise rejection:', error);
});

process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught exception:', error);
    process.exit(1);
});

// Connect to MongoDB
async function connectToDatabase() {
    try {
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ Connected to MongoDB');
    } catch (error) {
        console.error('❌ MongoDB connection error:', error);
        process.exit(1);
    }
}

// Schedule monthly rewards (runs on the 1st of each month at 00:00)
cron.schedule('0 0 1 * *', () => {
    console.log('🎁 Running monthly reward distribution...');
    rewardMonthly.distributeMonthlyRewards(client);
}, {
    timezone: "UTC"
});

// Initialize bot
async function startBot() {
    try {
        await connectToDatabase();
        await client.login(process.env.BOT_TOKEN);
    } catch (error) {
        console.error('❌ Error starting bot:', error);
        process.exit(1);
    }
}

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('🛑 Shutting down bot...');
    
    try {
        await mongoose.connection.close();
        console.log('✅ MongoDB connection closed');
        
        client.destroy();
        console.log('✅ Discord client destroyed');
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
    }
});

// Start the bot
startBot();
