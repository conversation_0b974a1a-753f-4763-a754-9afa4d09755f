const { Tail } = require('tail');
const fs = require('fs');
const path = require('path');

class MinecraftChatWatcher {
    constructor() {
        this.tail = null;
        this.client = null;
        this.chatChannel = null;
        this.isWatching = false;
        
        // Regex patterns for different log types
        this.patterns = {
            chat: /\[(\d{2}:\d{2}:\d{2})\] \[Server thread\/INFO\]: <([^>]+)> (.+)/,
            join: /\[(\d{2}:\d{2}:\d{2})\] \[Server thread\/INFO\]: ([^\s]+) joined the game/,
            leave: /\[(\d{2}:\d{2}:\d{2})\] \[Server thread\/INFO\]: ([^\s]+) left the game/,
            death: /\[(\d{2}:\d{2}:\d{2})\] \[Server thread\/INFO\]: ([^\s]+) (was|died|fell|burned|drowned|suffocated|starved|froze|withered)/,
            advancement: /\[(\d{2}:\d{2}:\d{2})\] \[Server thread\/INFO\]: ([^\s]+) has made the advancement \[(.+)\]/,
            serverStart: /\[(\d{2}:\d{2}:\d{2})\] \[Server thread\/INFO\]: Done \([\d.]+s\)! For help, type "help"/,
            serverStop: /\[(\d{2}:\d{2}:\d{2})\] \[Server thread\/INFO\]: Stopping server/
        };
    }

    /**
     * Initialize the chat watcher
     * @param {Client} discordClient - Discord client instance
     */
    initialize(discordClient) {
        this.client = discordClient;
        
        // Get chat channel
        const channelId = process.env.MC_CHAT_CHANNEL_ID;
        if (channelId) {
            this.chatChannel = this.client.channels.cache.get(channelId);
            if (!this.chatChannel) {
                console.warn('⚠️ MC chat channel not found. Chat bridging disabled.');
                return;
            }
        } else {
            console.warn('⚠️ MC_CHAT_CHANNEL_ID not set. Chat bridging disabled.');
            return;
        }

        // Start watching log file
        this.startWatching();
    }

    /**
     * Start watching the Minecraft log file
     */
    startWatching() {
        const logPath = process.env.MC_LOG_PATH;
        
        if (!logPath) {
            console.warn('⚠️ MC_LOG_PATH not set. Log watching disabled.');
            return;
        }

        if (!fs.existsSync(logPath)) {
            console.warn(`⚠️ Log file not found: ${logPath}`);
            return;
        }

        try {
            this.tail = new Tail(logPath, {
                separator: /[\r]{0,1}\n/,
                fromBeginning: false,
                fsWatchOptions: {},
                follow: true
            });

            this.tail.on('line', (line) => {
                this.processLogLine(line);
            });

            this.tail.on('error', (error) => {
                console.error('❌ Error watching log file:', error);
                this.attemptReconnect();
            });

            this.isWatching = true;
            console.log('✅ Started watching Minecraft log file');
        } catch (error) {
            console.error('❌ Failed to start log watcher:', error);
        }
    }

    /**
     * Process a log line and send to Discord if relevant
     * @param {string} line - Log line to process
     */
    async processLogLine(line) {
        if (!this.chatChannel) return;

        try {
            // Check for chat messages
            const chatMatch = line.match(this.patterns.chat);
            if (chatMatch) {
                const [, time, username, message] = chatMatch;
                await this.sendChatMessage(username, message, time);
                return;
            }

            // Check for player join
            const joinMatch = line.match(this.patterns.join);
            if (joinMatch) {
                const [, time, username] = joinMatch;
                await this.sendSystemMessage(`🟢 **${username}** joined the server`, 0x00FF00);
                return;
            }

            // Check for player leave
            const leaveMatch = line.match(this.patterns.leave);
            if (leaveMatch) {
                const [, time, username] = leaveMatch;
                await this.sendSystemMessage(`🔴 **${username}** left the server`, 0xFF0000);
                return;
            }

            // Check for deaths
            const deathMatch = line.match(this.patterns.death);
            if (deathMatch) {
                const deathMessage = line.split(']: ')[1];
                await this.sendSystemMessage(`💀 ${deathMessage}`, 0x8B0000);
                return;
            }

            // Check for advancements
            const advancementMatch = line.match(this.patterns.advancement);
            if (advancementMatch) {
                const [, time, username, advancement] = advancementMatch;
                await this.sendSystemMessage(`🏆 **${username}** has made the advancement **${advancement}**`, 0xFFD700);
                return;
            }

            // Check for server start
            const startMatch = line.match(this.patterns.serverStart);
            if (startMatch) {
                await this.sendSystemMessage('🟢 **Server Started**', 0x00FF00);
                return;
            }

            // Check for server stop
            const stopMatch = line.match(this.patterns.serverStop);
            if (stopMatch) {
                await this.sendSystemMessage('🔴 **Server Stopping**', 0xFF0000);
                return;
            }
        } catch (error) {
            console.error('❌ Error processing log line:', error);
        }
    }

    /**
     * Send a chat message to Discord
     * @param {string} username - Minecraft username
     * @param {string} message - Chat message
     * @param {string} time - Timestamp
     */
    async sendChatMessage(username, message, time) {
        try {
            // Get user's Discord avatar if linked
            const User = require('../models/User');
            const user = await User.findOne({ minecraftUsername: username });
            
            let avatarURL = `https://mc-heads.net/avatar/${username}/32`;
            let displayName = username;
            
            if (user) {
                try {
                    const discordUser = await this.client.users.fetch(user.discordId);
                    if (discordUser) {
                        avatarURL = discordUser.displayAvatarURL({ size: 32 });
                        displayName = `${username} (${discordUser.displayName || discordUser.username})`;
                    }
                } catch (error) {
                    // Use default Minecraft avatar if Discord user not found
                }
            }

            const embed = {
                color: 0x00AE86,
                author: {
                    name: displayName,
                    icon_url: avatarURL
                },
                description: message,
                timestamp: new Date().toISOString(),
                footer: {
                    text: `Minecraft • ${time}`
                }
            };

            await this.chatChannel.send({ embeds: [embed] });
        } catch (error) {
            console.error('❌ Error sending chat message to Discord:', error);
        }
    }

    /**
     * Send a system message to Discord
     * @param {string} message - System message
     * @param {number} color - Embed color
     */
    async sendSystemMessage(message, color = 0x808080) {
        try {
            const embed = {
                color: color,
                description: message,
                timestamp: new Date().toISOString(),
                footer: {
                    text: 'Minecraft Server'
                }
            };

            await this.chatChannel.send({ embeds: [embed] });
        } catch (error) {
            console.error('❌ Error sending system message to Discord:', error);
        }
    }

    /**
     * Attempt to reconnect the log watcher
     */
    async attemptReconnect() {
        console.log('🔄 Attempting to reconnect log watcher...');
        
        if (this.tail) {
            try {
                this.tail.unwatch();
            } catch (error) {
                console.error('❌ Error unwatching log file:', error);
            }
        }

        // Wait 5 seconds before reconnecting
        setTimeout(() => {
            this.startWatching();
        }, 5000);
    }

    /**
     * Stop watching the log file
     */
    stop() {
        if (this.tail) {
            try {
                this.tail.unwatch();
                this.isWatching = false;
                console.log('✅ Stopped watching Minecraft log file');
            } catch (error) {
                console.error('❌ Error stopping log watcher:', error);
            }
        }
    }

    /**
     * Get current watching status
     * @returns {boolean} - Whether currently watching
     */
    getStatus() {
        return this.isWatching;
    }
}

module.exports = new MinecraftChatWatcher();
