const { REST, Routes } = require('discord.js');

class CommandHandler {
    constructor() {
        this.rest = new REST({ version: '10' }).setToken(process.env.BOT_TOKEN);
    }

    /**
     * Register slash commands with Discord
     * @param {Client} client - Discord client instance
     */
    async registerCommands(client) {
        const commands = [];
        
        // Collect command data from all loaded commands
        client.commands.forEach(command => {
            commands.push(command.data.toJSON());
        });

        try {
            console.log(`🔄 Started refreshing ${commands.length} application (/) commands.`);

            // Register commands globally or to a specific guild
            if (process.env.GUILD_ID) {
                // Register to specific guild (faster for development)
                await this.rest.put(
                    Routes.applicationGuildCommands(process.env.CLIENT_ID, process.env.GUILD_ID),
                    { body: commands }
                );
                console.log(`✅ Successfully reloaded ${commands.length} guild application (/) commands.`);
            } else {
                // Register globally (takes up to 1 hour to propagate)
                await this.rest.put(
                    Routes.applicationCommands(process.env.CLIENT_ID),
                    { body: commands }
                );
                console.log(`✅ Successfully reloaded ${commands.length} global application (/) commands.`);
            }
        } catch (error) {
            console.error('❌ Error registering commands:', error);
            throw error;
        }
    }

    /**
     * Check if user has required permissions for restricted commands
     * @param {CommandInteraction} interaction - Discord interaction
     * @param {string} requiredLevel - Required permission level ('admin', 'moderator', 'owner')
     * @returns {boolean} - Whether user has required permissions
     */
    static hasPermission(interaction, requiredLevel = 'member') {
        const member = interaction.member;
        const userId = interaction.user.id;

        switch (requiredLevel) {
            case 'owner':
                return userId === process.env.OWNER_ID;
            
            case 'admin':
                return userId === process.env.OWNER_ID || 
                       member.roles.cache.has(process.env.ADMIN_ROLE_ID) ||
                       member.permissions.has('Administrator');
            
            case 'moderator':
                return userId === process.env.OWNER_ID || 
                       member.roles.cache.has(process.env.ADMIN_ROLE_ID) ||
                       member.roles.cache.has(process.env.MODERATOR_ROLE_ID) ||
                       member.permissions.has('Administrator') ||
                       member.permissions.has('ManageMessages');
            
            case 'member':
            default:
                return true;
        }
    }

    /**
     * Send permission denied message
     * @param {CommandInteraction} interaction - Discord interaction
     * @param {string} requiredLevel - Required permission level
     */
    static async sendPermissionDenied(interaction, requiredLevel) {
        const embed = {
            color: 0xFF0000,
            title: '🚫 Access Denied',
            description: `This command requires **${requiredLevel}** permissions.`,
            timestamp: new Date().toISOString(),
            footer: {
                text: 'The Basement Bot'
            }
        };

        await interaction.reply({
            embeds: [embed],
            ephemeral: true
        });
    }

    /**
     * Validate command permissions before execution
     * @param {CommandInteraction} interaction - Discord interaction
     * @param {string} requiredLevel - Required permission level
     * @returns {boolean} - Whether to continue with command execution
     */
    static async validatePermissions(interaction, requiredLevel) {
        if (!this.hasPermission(interaction, requiredLevel)) {
            await this.sendPermissionDenied(interaction, requiredLevel);
            return false;
        }
        return true;
    }

    /**
     * Log command usage
     * @param {CommandInteraction} interaction - Discord interaction
     * @param {string} commandName - Name of the executed command
     */
    static logCommandUsage(interaction, commandName) {
        const user = interaction.user;
        const guild = interaction.guild;
        const channel = interaction.channel;
        
        console.log(`📝 Command used: /${commandName} by ${user.tag} (${user.id}) in ${guild?.name || 'DM'} #${channel?.name || 'DM'}`);
    }

    /**
     * Handle command errors consistently
     * @param {CommandInteraction} interaction - Discord interaction
     * @param {Error} error - The error that occurred
     * @param {string} commandName - Name of the command that failed
     */
    static async handleCommandError(interaction, error, commandName) {
        console.error(`❌ Error in command ${commandName}:`, error);

        const embed = {
            color: 0xFF0000,
            title: '❌ Command Error',
            description: 'An error occurred while executing this command. Please try again later.',
            timestamp: new Date().toISOString(),
            footer: {
                text: 'The Basement Bot'
            }
        };

        // Add error details for owner
        if (interaction.user.id === process.env.OWNER_ID) {
            embed.fields = [{
                name: 'Error Details',
                value: `\`\`\`${error.message}\`\`\``,
                inline: false
            }];
        }

        try {
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [embed], ephemeral: true });
            } else {
                await interaction.reply({ embeds: [embed], ephemeral: true });
            }
        } catch (followUpError) {
            console.error('❌ Error sending error message:', followUpError);
        }
    }
}

module.exports = new CommandHandler();
