const { EmbedBuilder } = require('discord.js');

class AdvancedEmbedBuilder {
    /**
     * Create a basic embed with common styling
     * @param {Object} options - Embed options
     * @returns {EmbedBuilder} - Discord embed builder
     */
    static createBasicEmbed(options = {}) {
        const embed = new EmbedBuilder()
            .setColor(options.color || 0x00AE86)
            .setTimestamp()
            .setFooter({ 
                text: options.footerText || 'The Basement Bot',
                iconURL: options.footerIcon || null
            });

        if (options.title) embed.setTitle(options.title);
        if (options.description) embed.setDescription(options.description);
        if (options.thumbnail) embed.setThumbnail(options.thumbnail);
        if (options.image) embed.setImage(options.image);
        if (options.author) {
            embed.setAuthor({
                name: options.author.name,
                iconURL: options.author.iconURL || null,
                url: options.author.url || null
            });
        }

        return embed;
    }

    /**
     * Create a success embed
     * @param {string} title - Embed title
     * @param {string} description - Embed description
     * @param {Object} options - Additional options
     * @returns {EmbedBuilder} - Success embed
     */
    static createSuccessEmbed(title, description, options = {}) {
        return this.createBasicEmbed({
            title: `✅ ${title}`,
            description,
            color: 0x00FF00,
            ...options
        });
    }

    /**
     * Create an error embed
     * @param {string} title - Embed title
     * @param {string} description - Embed description
     * @param {Object} options - Additional options
     * @returns {EmbedBuilder} - Error embed
     */
    static createErrorEmbed(title, description, options = {}) {
        return this.createBasicEmbed({
            title: `❌ ${title}`,
            description,
            color: 0xFF0000,
            ...options
        });
    }

    /**
     * Create a warning embed
     * @param {string} title - Embed title
     * @param {string} description - Embed description
     * @param {Object} options - Additional options
     * @returns {EmbedBuilder} - Warning embed
     */
    static createWarningEmbed(title, description, options = {}) {
        return this.createBasicEmbed({
            title: `⚠️ ${title}`,
            description,
            color: 0xFFFF00,
            ...options
        });
    }

    /**
     * Create an info embed
     * @param {string} title - Embed title
     * @param {string} description - Embed description
     * @param {Object} options - Additional options
     * @returns {EmbedBuilder} - Info embed
     */
    static createInfoEmbed(title, description, options = {}) {
        return this.createBasicEmbed({
            title: `ℹ️ ${title}`,
            description,
            color: 0x0099FF,
            ...options
        });
    }

    /**
     * Create a slot machine result embed
     * @param {string} result - Slot result (common, rare, epic, legendary)
     * @param {Object} reward - Reward details
     * @param {Object} user - Discord user object
     * @returns {EmbedBuilder} - Slot machine embed
     */
    static createSlotMachineEmbed(result, reward, user) {
        const colors = {
            common: 0x0099FF,    // Blue
            rare: 0x8B4513,      // Brown
            epic: 0x800080,      // Purple
            legendary: 0xFFD700   // Gold
        };

        const emojis = {
            common: '🟦',
            rare: '🟫',
            epic: '🟪',
            legendary: '🟨'
        };

        const embed = this.createBasicEmbed({
            title: '🎰 Slot Machine Results',
            color: colors[result.toLowerCase()],
            author: {
                name: user.displayName || user.username,
                iconURL: user.displayAvatarURL()
            }
        });

        embed.addFields(
            {
                name: 'Result',
                value: `${emojis[result.toLowerCase()]} **${result.toUpperCase()}**`,
                inline: true
            },
            {
                name: 'Reward',
                value: reward.description || 'Mystery reward!',
                inline: false
            }
        );

        if (process.env.SLOT_MACHINE_GIF_URL) {
            embed.setImage(process.env.SLOT_MACHINE_GIF_URL);
        }

        return embed;
    }

    /**
     * Create a daily login reward embed
     * @param {number} day - Day number (1-7)
     * @param {Object} reward - Reward details
     * @param {Object} user - Discord user object
     * @returns {EmbedBuilder} - Daily login embed
     */
    static createDailyLoginEmbed(day, reward, user) {
        const dayNames = {
            1: 'Starter',
            2: 'Defender', 
            3: 'Tool Set',
            4: 'Ranger',
            5: 'Alchemist',
            6: 'Survivor',
            7: 'Mega Chest - Exalted'
        };

        const embed = this.createSuccessEmbed(
            'Daily Login Reward',
            `Congratulations! You've claimed your Day ${day} reward.`,
            {
                author: {
                    name: user.displayName || user.username,
                    iconURL: user.displayAvatarURL()
                }
            }
        );

        embed.addFields(
            {
                name: `Day ${day} - ${dayNames[day]}`,
                value: reward.description,
                inline: false
            },
            {
                name: 'Next Reward',
                value: day === 7 ? 'Cycle resets to Day 1' : `Day ${day + 1} - ${dayNames[day + 1]}`,
                inline: true
            }
        );

        if (day === 7) {
            embed.setColor(0xFFD700); // Gold for mega chest
            embed.setThumbnail('https://minecraft.wiki/images/thumb/0/0a/Chest_%28S%29_JE2_BE1.png/150px-Chest_%28S%29_JE2_BE1.png');
        }

        return embed;
    }

    /**
     * Create a leaderboard embed
     * @param {Array} users - Array of user data
     * @param {string} type - Leaderboard type
     * @returns {EmbedBuilder} - Leaderboard embed
     */
    static createLeaderboardEmbed(users, type = 'general') {
        const embed = this.createBasicEmbed({
            title: `🏆 ${type.charAt(0).toUpperCase() + type.slice(1)} Leaderboard`,
            color: 0xFFD700
        });

        if (users.length === 0) {
            embed.setDescription('No data available yet. Start playing to appear on the leaderboard!');
            return embed;
        }

        const leaderboardText = users.slice(0, 10).map((user, index) => {
            const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
            return `${medal} **${user.minecraftUsername || 'Unknown'}** - ${user.value}`;
        }).join('\n');

        embed.setDescription(leaderboardText);

        return embed;
    }

    /**
     * Create a help embed
     * @param {Array} commands - Array of command information
     * @returns {EmbedBuilder} - Help embed
     */
    static createHelpEmbed(commands) {
        const embed = this.createBasicEmbed({
            title: '📚 Bot Commands',
            description: 'Here are all the available commands:',
            color: 0x00AE86
        });

        // Group commands by category
        const categories = {
            'General': [],
            'Rewards': [],
            'Admin': []
        };

        commands.forEach(cmd => {
            const category = cmd.category || 'General';
            if (categories[category]) {
                categories[category].push(`\`/${cmd.name}\` - ${cmd.description}`);
            }
        });

        // Add fields for each category
        Object.entries(categories).forEach(([category, cmds]) => {
            if (cmds.length > 0) {
                embed.addFields({
                    name: category,
                    value: cmds.join('\n'),
                    inline: false
                });
            }
        });

        return embed;
    }

    /**
     * Create an announcement embed
     * @param {string} title - Announcement title
     * @param {string} message - Announcement message
     * @param {Object} options - Additional options
     * @returns {EmbedBuilder} - Announcement embed
     */
    static createAnnouncementEmbed(title, message, options = {}) {
        const embed = this.createBasicEmbed({
            title: `📢 ${title}`,
            description: message,
            color: options.color || 0xFF6B35,
            ...options
        });

        if (options.image) {
            embed.setImage(options.image);
        }

        if (options.fields) {
            embed.addFields(options.fields);
        }

        return embed;
    }

    /**
     * Create a poll embed
     * @param {string} question - Poll question
     * @param {Array} options - Poll options
     * @param {Object} author - Poll author
     * @returns {EmbedBuilder} - Poll embed
     */
    static createPollEmbed(question, options, author) {
        const embed = this.createBasicEmbed({
            title: '📊 Poll',
            description: `**${question}**\n\n${options.map((option, index) => 
                `${index + 1}️⃣ ${option}`
            ).join('\n')}`,
            color: 0x9B59B6,
            author: {
                name: `Poll by ${author.displayName || author.username}`,
                iconURL: author.displayAvatarURL()
            }
        });

        embed.addFields({
            name: 'How to Vote',
            value: 'React with the corresponding number emoji to vote!',
            inline: false
        });

        return embed;
    }
}

module.exports = AdvancedEmbedBuilder;
