const { Rcon } = require('rcon-client');

class RconHandler {
    constructor() {
        this.rcon = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 5000; // 5 seconds
    }

    /**
     * Connect to Minecraft server via RCON
     */
    async connect() {
        try {
            if (this.isConnected && this.rcon) {
                return true;
            }

            this.rcon = await Rcon.connect({
                host: process.env.RCON_HOST,
                port: parseInt(process.env.RCON_PORT) || 25575,
                password: process.env.RCON_PASSWORD
            });

            this.isConnected = true;
            this.reconnectAttempts = 0;
            console.log('✅ Connected to Minecraft server via RCON');
            return true;
        } catch (error) {
            console.error('❌ Failed to connect to RCON:', error);
            this.isConnected = false;
            return false;
        }
    }

    /**
     * Disconnect from RCON
     */
    async disconnect() {
        try {
            if (this.rcon) {
                await this.rcon.end();
                this.rcon = null;
            }
            this.isConnected = false;
            console.log('✅ Disconnected from RCON');
        } catch (error) {
            console.error('❌ Error disconnecting from RCON:', error);
        }
    }

    /**
     * Execute a command on the Minecraft server
     * @param {string} command - The command to execute
     * @returns {Promise<string>} - The response from the server
     */
    async executeCommand(command) {
        try {
            // Ensure connection
            if (!this.isConnected) {
                const connected = await this.connect();
                if (!connected) {
                    throw new Error('Failed to connect to RCON');
                }
            }

            const response = await this.rcon.send(command);
            console.log(`📤 RCON Command: ${command}`);
            console.log(`📥 RCON Response: ${response}`);
            return response;
        } catch (error) {
            console.error(`❌ RCON command failed: ${command}`, error);
            this.isConnected = false;
            
            // Attempt to reconnect
            await this.attemptReconnect();
            throw error;
        }
    }

    /**
     * Attempt to reconnect to RCON
     */
    async attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('❌ Max reconnection attempts reached');
            return false;
        }

        this.reconnectAttempts++;
        console.log(`🔄 Attempting to reconnect to RCON (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        
        await new Promise(resolve => setTimeout(resolve, this.reconnectDelay));
        return await this.connect();
    }

    /**
     * Give items to a player
     * @param {string} playerName - Minecraft username
     * @param {string} item - Item to give (e.g., "diamond 5")
     * @returns {Promise<string>} - Server response
     */
    async giveItem(playerName, item) {
        const command = `give ${playerName} ${item}`;
        return await this.executeCommand(command);
    }

    /**
     * Send a message to all players
     * @param {string} message - Message to send
     * @returns {Promise<string>} - Server response
     */
    async broadcastMessage(message) {
        const command = `say ${message}`;
        return await this.executeCommand(command);
    }

    /**
     * Send a private message to a player
     * @param {string} playerName - Target player
     * @param {string} message - Message to send
     * @returns {Promise<string>} - Server response
     */
    async tellPlayer(playerName, message) {
        const command = `tell ${playerName} ${message}`;
        return await this.executeCommand(command);
    }

    /**
     * Execute multiple commands in sequence
     * @param {string[]} commands - Array of commands to execute
     * @returns {Promise<string[]>} - Array of responses
     */
    async executeMultipleCommands(commands) {
        const responses = [];
        for (const command of commands) {
            try {
                const response = await this.executeCommand(command);
                responses.push(response);
            } catch (error) {
                console.error(`❌ Failed to execute command: ${command}`, error);
                responses.push(`Error: ${error.message}`);
            }
        }
        return responses;
    }

    /**
     * Give reward items to a player based on reward type
     * @param {string} playerName - Minecraft username
     * @param {string} rewardType - Type of reward (common, rare, epic, legendary)
     * @returns {Promise<boolean>} - Success status
     */
    async giveReward(playerName, rewardType) {
        try {
            let commands = [];

            switch (rewardType.toLowerCase()) {
                case 'common':
                    commands = [
                        `give ${playerName} experience_bottle 20`,
                        `give ${playerName} arrow 10`,
                        `give ${playerName} cooked_beef 5`
                    ];
                    break;

                case 'rare':
                    commands = [
                        `give ${playerName} diamond_block 1`,
                        `give ${playerName} experience_bottle 30`,
                        `give ${playerName} enchanted_book{StoredEnchantments:[{id:"minecraft:sharpness",lvl:2}]} 1`
                    ];
                    break;

                case 'epic':
                    commands = [
                        `give ${playerName} diamond_sword{Enchantments:[{id:"minecraft:sharpness",lvl:3},{id:"minecraft:unbreaking",lvl:2}]} 1`,
                        `give ${playerName} experience_bottle 50`,
                        `give ${playerName} potion{Potion:"minecraft:swiftness"} 1`
                    ];
                    break;

                case 'legendary':
                    commands = [
                        `give ${playerName} beacon 1`,
                        `give ${playerName} diamond_sword{Enchantments:[{id:"minecraft:sharpness",lvl:4}]} 1`,
                        `give ${playerName} diamond_helmet{Enchantments:[{id:"minecraft:protection",lvl:4}]} 1`,
                        `give ${playerName} diamond_chestplate{Enchantments:[{id:"minecraft:protection",lvl:4}]} 1`,
                        `give ${playerName} diamond_leggings{Enchantments:[{id:"minecraft:protection",lvl:4}]} 1`,
                        `give ${playerName} diamond_boots{Enchantments:[{id:"minecraft:protection",lvl:4}]} 1`,
                        `give ${playerName} elytra{Enchantments:[{id:"minecraft:unbreaking",lvl:3}]} 1`,
                        `give ${playerName} experience_bottle 200`
                    ];
                    break;

                default:
                    throw new Error(`Unknown reward type: ${rewardType}`);
            }

            await this.executeMultipleCommands(commands);
            await this.tellPlayer(playerName, `§6[Basement Bot] §aYou received a ${rewardType.toUpperCase()} reward!`);
            return true;
        } catch (error) {
            console.error(`❌ Failed to give ${rewardType} reward to ${playerName}:`, error);
            return false;
        }
    }

    /**
     * Give daily login reward to a player
     * @param {string} playerName - Minecraft username
     * @param {number} day - Day number (1-7)
     * @returns {Promise<boolean>} - Success status
     */
    async giveDailyReward(playerName, day) {
        try {
            let commands = [];
            let rewardName = '';

            switch (day) {
                case 1: // Starter
                    commands = [
                        `give ${playerName} cooked_beef 5`,
                        `give ${playerName} bread 5`
                    ];
                    rewardName = 'Starter';
                    break;

                case 2: // Defender
                    commands = [
                        `give ${playerName} shield 1`,
                        `give ${playerName} arrow 10`
                    ];
                    rewardName = 'Defender';
                    break;

                case 3: // Tool Set
                    commands = [
                        `give ${playerName} iron_axe 1`,
                        `give ${playerName} iron_ingot 5`
                    ];
                    rewardName = 'Tool Set';
                    break;

                case 4: // Ranger
                    commands = [
                        `give ${playerName} bow{Enchantments:[{id:"minecraft:power",lvl:1}]} 1`,
                        `give ${playerName} experience_bottle 3`
                    ];
                    rewardName = 'Ranger';
                    break;

                case 5: // Alchemist
                    const potions = ['healing', 'strength', 'fire_resistance'];
                    const randomPotion = potions[Math.floor(Math.random() * potions.length)];
                    commands = [
                        `give ${playerName} potion{Potion:"minecraft:${randomPotion}"} 1`
                    ];
                    rewardName = 'Alchemist';
                    break;

                case 6: // Survivor
                    commands = [
                        `give ${playerName} totem_of_undying 1`,
                        `give ${playerName} experience_bottle 20`
                    ];
                    rewardName = 'Survivor';
                    break;

                case 7: // Mega Chest - Exalted
                    commands = [
                        `give ${playerName} diamond 10`,
                        `give ${playerName} experience_bottle 64`,
                        `give ${playerName} potion{Potion:"minecraft:healing"} 3`,
                        `give ${playerName} enchanted_golden_apple 2`,
                        `give ${playerName} netherite_ingot 1`
                    ];
                    // 10% chance for extra totem
                    if (Math.random() < 0.1) {
                        commands.push(`give ${playerName} totem_of_undying 1`);
                    }
                    rewardName = 'Mega Chest - Exalted';
                    break;

                default:
                    throw new Error(`Invalid day: ${day}`);
            }

            await this.executeMultipleCommands(commands);
            await this.tellPlayer(playerName, `§6[Basement Bot] §aDay ${day} reward (${rewardName}) claimed!`);
            return true;
        } catch (error) {
            console.error(`❌ Failed to give daily reward to ${playerName}:`, error);
            return false;
        }
    }

    /**
     * Check if the RCON connection is healthy
     * @returns {Promise<boolean>} - Connection status
     */
    async isHealthy() {
        try {
            if (!this.isConnected) return false;
            await this.executeCommand('list');
            return true;
        } catch (error) {
            return false;
        }
    }
}

module.exports = new RconHandler();
