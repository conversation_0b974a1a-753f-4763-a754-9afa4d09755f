const User = require('../models/User');
const rconHandler = require('./rconHandler');
const AdvancedEmbedBuilder = require('./createAdvancedEmbed');

class MonthlyRewardSystem {
    constructor() {
        this.premiumRewards = [
            {
                name: 'Netherite Sword',
                commands: [
                    'give {player} netherite_sword{Enchantments:[{id:"minecraft:sharpness",lvl:5},{id:"minecraft:unbreaking",lvl:3},{id:"minecraft:looting",lvl:3}]} 1'
                ],
                description: 'Netherite Sword with Sharpness V, Unbreaking III, and Looting III'
            },
            {
                name: '<PERSON>tra Wings',
                commands: [
                    'give {player} elytra{Enchantments:[{id:"minecraft:unbreaking",lvl:3}]} 1'
                ],
                description: '<PERSON><PERSON> with Unbreaking III'
            },
            {
                name: 'Full Netherite Armor Set',
                commands: [
                    'give {player} netherite_helmet{Enchantments:[{id:"minecraft:protection",lvl:4},{id:"minecraft:unbreaking",lvl:3}]} 1',
                    'give {player} netherite_chestplate{Enchantments:[{id:"minecraft:protection",lvl:4},{id:"minecraft:unbreaking",lvl:3}]} 1',
                    'give {player} netherite_leggings{Enchantments:[{id:"minecraft:protection",lvl:4},{id:"minecraft:unbreaking",lvl:3}]} 1',
                    'give {player} netherite_boots{Enchantments:[{id:"minecraft:protection",lvl:4},{id:"minecraft:unbreaking",lvl:3}]} 1'
                ],
                description: 'Full Netherite Armor Set with Protection IV and Unbreaking III'
            },
            {
                name: 'Beacon',
                commands: [
                    'give {player} beacon 1',
                    'give {player} iron_block 9'
                ],
                description: 'Beacon with iron blocks for pyramid base'
            }
        ];

        this.boosterTitles = [
            'Boosted Champion',
            'Discord Elite',
            'Premium Supporter',
            'VIP Member'
        ];
    }

    /**
     * Distribute monthly rewards to eligible users
     * @param {Client} client - Discord client instance
     */
    async distributeMonthlyRewards(client) {
        try {
            console.log('🎁 Starting monthly reward distribution...');
            
            // Find users with full month login streak (30+ days)
            const eligibleUsers = await this.findEligibleUsers();
            
            if (eligibleUsers.length === 0) {
                console.log('📝 No users eligible for monthly rewards');
                return;
            }

            console.log(`📊 Found ${eligibleUsers.length} eligible users for monthly rewards`);

            const rewardResults = [];

            for (const user of eligibleUsers) {
                try {
                    const result = await this.giveMonthlyReward(user, client);
                    rewardResults.push(result);
                } catch (error) {
                    console.error(`❌ Error giving monthly reward to ${user.minecraftUsername}:`, error);
                    rewardResults.push({
                        user: user,
                        success: false,
                        error: error.message
                    });
                }
            }

            // Send summary to admin channel or log
            await this.sendRewardSummary(client, rewardResults);
            
            console.log('✅ Monthly reward distribution completed');
        } catch (error) {
            console.error('❌ Error in monthly reward distribution:', error);
        }
    }

    /**
     * Find users eligible for monthly rewards
     * @returns {Array} - Array of eligible users
     */
    async findEligibleUsers() {
        try {
            // Find users who have logged in for at least 25 days this month
            const startOfMonth = new Date();
            startOfMonth.setDate(1);
            startOfMonth.setHours(0, 0, 0, 0);

            const users = await User.find({
                minecraftUsername: { $exists: true, $ne: null },
                'dailyLogin.lastLogin': { $gte: startOfMonth },
                monthlyStreak: { $gte: 25 }
            });

            return users;
        } catch (error) {
            console.error('❌ Error finding eligible users:', error);
            return [];
        }
    }

    /**
     * Give monthly reward to a specific user
     * @param {Object} user - User document from database
     * @param {Client} client - Discord client instance
     * @returns {Object} - Reward result
     */
    async giveMonthlyReward(user, client) {
        try {
            // Select random premium reward
            const reward = this.premiumRewards[Math.floor(Math.random() * this.premiumRewards.length)];
            
            // Execute reward commands
            const commands = reward.commands.map(cmd => cmd.replace('{player}', user.minecraftUsername));
            await rconHandler.executeMultipleCommands(commands);

            // Check if user is a booster
            let isBooster = user.booster;
            if (!isBooster && client) {
                try {
                    const discordUser = await client.users.fetch(user.discordId);
                    const guild = client.guilds.cache.first(); // Assuming single guild bot
                    if (guild) {
                        const member = await guild.members.fetch(discordUser.id);
                        isBooster = member.premiumSince !== null;
                        
                        // Update user's booster status
                        user.booster = isBooster;
                        await user.save();
                    }
                } catch (error) {
                    console.warn(`⚠️ Could not check booster status for ${user.minecraftUsername}`);
                }
            }

            // Give booster perks if applicable
            if (isBooster) {
                await this.giveBoosterPerks(user.minecraftUsername);
            }

            // Reset monthly streak
            user.monthlyStreak = 0;
            await user.save();

            // Send notification to Minecraft
            await rconHandler.tellPlayer(
                user.minecraftUsername,
                `§6[Monthly Reward] §aYou received: ${reward.name}! ${isBooster ? '§d(+Booster Bonus!)' : ''}`
            );

            console.log(`✅ Monthly reward given to ${user.minecraftUsername}: ${reward.name}`);

            return {
                user: user,
                reward: reward,
                isBooster: isBooster,
                success: true
            };
        } catch (error) {
            console.error(`❌ Error giving monthly reward to ${user.minecraftUsername}:`, error);
            throw error;
        }
    }

    /**
     * Give additional perks to booster users
     * @param {string} playerName - Minecraft username
     */
    async giveBoosterPerks(playerName) {
        try {
            const boosterCommands = [
                `give ${playerName} netherite_ingot 5`,
                `give ${playerName} enchanted_golden_apple 3`,
                `give ${playerName} experience_bottle 100`,
                `give ${playerName} totem_of_undying 2`
            ];

            await rconHandler.executeMultipleCommands(boosterCommands);

            // Give special title (if using a plugin that supports it)
            const randomTitle = this.boosterTitles[Math.floor(Math.random() * this.boosterTitles.length)];
            
            // This would require a plugin like EssentialsX or similar
            // await rconHandler.executeCommand(`nick ${playerName} §d[${randomTitle}]§f ${playerName}`);

            await rconHandler.tellPlayer(
                playerName,
                `§d[Booster Bonus] §aExtra rewards for being a Discord booster! Thank you for your support! 💜`
            );

            console.log(`✅ Booster perks given to ${playerName}`);
        } catch (error) {
            console.error(`❌ Error giving booster perks to ${playerName}:`, error);
        }
    }

    /**
     * Send reward distribution summary
     * @param {Client} client - Discord client instance
     * @param {Array} results - Array of reward results
     */
    async sendRewardSummary(client, results) {
        try {
            const successCount = results.filter(r => r.success).length;
            const failureCount = results.length - successCount;
            const boosterCount = results.filter(r => r.success && r.isBooster).length;

            const embed = AdvancedEmbedBuilder.createBasicEmbed({
                title: '🎁 Monthly Reward Distribution Summary',
                color: 0xFFD700,
                description: `Monthly rewards have been distributed for ${new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}`
            });

            embed.addFields(
                {
                    name: '📊 Statistics',
                    value: [
                        `✅ Successful: ${successCount}`,
                        `❌ Failed: ${failureCount}`,
                        `💜 Boosters: ${boosterCount}`,
                        `👥 Total Eligible: ${results.length}`
                    ].join('\n'),
                    inline: true
                }
            );

            if (successCount > 0) {
                const rewardBreakdown = {};
                results.filter(r => r.success).forEach(r => {
                    const rewardName = r.reward.name;
                    rewardBreakdown[rewardName] = (rewardBreakdown[rewardName] || 0) + 1;
                });

                const rewardList = Object.entries(rewardBreakdown)
                    .map(([name, count]) => `${name}: ${count}`)
                    .join('\n');

                embed.addFields({
                    name: '🎁 Rewards Given',
                    value: rewardList,
                    inline: true
                });
            }

            if (failureCount > 0) {
                const failedUsers = results
                    .filter(r => !r.success)
                    .map(r => r.user.minecraftUsername || 'Unknown')
                    .join(', ');

                embed.addFields({
                    name: '⚠️ Failed Distributions',
                    value: failedUsers,
                    inline: false
                });
            }

            // Try to send to admin channel or log channel
            const adminChannelId = process.env.ADMIN_CHANNEL_ID || process.env.MC_CHAT_CHANNEL_ID;
            if (adminChannelId && client) {
                const channel = client.channels.cache.get(adminChannelId);
                if (channel) {
                    await channel.send({ embeds: [embed] });
                }
            }

            console.log(`📊 Monthly reward summary: ${successCount} successful, ${failureCount} failed`);
        } catch (error) {
            console.error('❌ Error sending reward summary:', error);
        }
    }

    /**
     * Manually trigger monthly rewards (admin function)
     * @param {Client} client - Discord client instance
     * @returns {Object} - Distribution results
     */
    async manualDistribution(client) {
        console.log('🔧 Manual monthly reward distribution triggered');
        await this.distributeMonthlyRewards(client);
        return { success: true, message: 'Manual distribution completed' };
    }

    /**
     * Get monthly reward statistics
     * @returns {Object} - Statistics object
     */
    async getStats() {
        try {
            const startOfMonth = new Date();
            startOfMonth.setDate(1);
            startOfMonth.setHours(0, 0, 0, 0);

            const eligibleCount = await User.countDocuments({
                minecraftUsername: { $exists: true, $ne: null },
                'dailyLogin.lastLogin': { $gte: startOfMonth },
                monthlyStreak: { $gte: 25 }
            });

            const totalUsers = await User.countDocuments({
                minecraftUsername: { $exists: true, $ne: null }
            });

            const boosterCount = await User.countDocuments({
                booster: true,
                minecraftUsername: { $exists: true, $ne: null }
            });

            return {
                eligibleUsers: eligibleCount,
                totalUsers: totalUsers,
                boosterUsers: boosterCount,
                eligibilityRate: totalUsers > 0 ? (eligibleCount / totalUsers * 100).toFixed(1) : 0,
                availableRewards: this.premiumRewards.length
            };
        } catch (error) {
            console.error('❌ Error getting monthly reward stats:', error);
            return null;
        }
    }
}

module.exports = new MonthlyRewardSystem();
