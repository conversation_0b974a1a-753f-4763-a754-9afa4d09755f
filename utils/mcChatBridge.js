const rconHandler = require('./rconHandler');

class MinecraftChatBridge {
    constructor() {
        this.maxMessageLength = 256; // Minecraft chat limit
        this.cooldowns = new Map(); // User cooldowns to prevent spam
        this.cooldownTime = 2000; // 2 seconds between messages
    }

    /**
     * Send a Discord message to Minecraft
     * @param {Message} message - Discord message object
     */
    async sendToMinecraft(message) {
        try {
            // Skip if user is on cooldown
            if (this.isOnCooldown(message.author.id)) {
                return;
            }

            // Set cooldown
            this.setCooldown(message.author.id);

            // Get user's linked Minecraft account
            const User = require('../models/User');
            const user = await User.findOne({ discordId: message.author.id });
            
            let displayName = message.author.displayName || message.author.username;
            
            // If user has linked account, use Minecraft username
            if (user && user.minecraftUsername) {
                displayName = user.minecraftUsername;
            }

            // Clean and format message
            let cleanMessage = this.cleanMessage(message.content);
            
            // Handle attachments
            if (message.attachments.size > 0) {
                const attachmentTypes = Array.from(message.attachments.values())
                    .map(att => this.getAttachmentType(att.name))
                    .filter(type => type);
                
                if (attachmentTypes.length > 0) {
                    cleanMessage += ` [${attachmentTypes.join(', ')}]`;
                }
            }

            // Handle replies
            if (message.reference) {
                try {
                    const repliedMessage = await message.channel.messages.fetch(message.reference.messageId);
                    const repliedUser = repliedMessage.author.displayName || repliedMessage.author.username;
                    cleanMessage = `@${repliedUser} ${cleanMessage}`;
                } catch (error) {
                    // Ignore if can't fetch replied message
                }
            }

            // Truncate if too long
            if (cleanMessage.length > this.maxMessageLength - displayName.length - 10) {
                cleanMessage = cleanMessage.substring(0, this.maxMessageLength - displayName.length - 13) + '...';
            }

            // Format final message
            const minecraftMessage = `§7[§bDiscord§7] §f<${displayName}> ${cleanMessage}`;

            // Send to Minecraft
            await rconHandler.executeCommand(`say ${minecraftMessage}`);
            
            console.log(`📤 Discord → Minecraft: ${displayName}: ${cleanMessage}`);
        } catch (error) {
            console.error('❌ Error bridging message to Minecraft:', error);
        }
    }

    /**
     * Clean Discord message for Minecraft
     * @param {string} content - Original message content
     * @returns {string} - Cleaned message
     */
    cleanMessage(content) {
        return content
            // Remove Discord mentions and replace with readable format
            .replace(/<@!?(\d+)>/g, '@user')
            .replace(/<@&(\d+)>/g, '@role')
            .replace(/<#(\d+)>/g, '#channel')
            // Remove Discord emojis
            .replace(/<a?:\w+:\d+>/g, ':emoji:')
            // Remove markdown formatting
            .replace(/\*\*(.*?)\*\*/g, '$1')
            .replace(/\*(.*?)\*/g, '$1')
            .replace(/__(.*?)__/g, '$1')
            .replace(/_(.*?)_/g, '$1')
            .replace(/~~(.*?)~~/g, '$1')
            .replace(/`(.*?)`/g, '$1')
            // Remove code blocks
            .replace(/```[\s\S]*?```/g, '[code block]')
            // Remove spoilers
            .replace(/\|\|(.*?)\|\|/g, '[spoiler]')
            // Clean up extra whitespace
            .replace(/\s+/g, ' ')
            .trim();
    }

    /**
     * Get attachment type for display
     * @param {string} filename - Attachment filename
     * @returns {string} - Attachment type
     */
    getAttachmentType(filename) {
        const extension = filename.split('.').pop().toLowerCase();
        
        const imageTypes = ['png', 'jpg', 'jpeg', 'gif', 'webp', 'bmp'];
        const videoTypes = ['mp4', 'webm', 'mov', 'avi', 'mkv'];
        const audioTypes = ['mp3', 'wav', 'ogg', 'flac', 'm4a'];
        const documentTypes = ['pdf', 'doc', 'docx', 'txt', 'rtf'];
        
        if (imageTypes.includes(extension)) return 'Image';
        if (videoTypes.includes(extension)) return 'Video';
        if (audioTypes.includes(extension)) return 'Audio';
        if (documentTypes.includes(extension)) return 'Document';
        
        return 'File';
    }

    /**
     * Check if user is on cooldown
     * @param {string} userId - Discord user ID
     * @returns {boolean} - Whether user is on cooldown
     */
    isOnCooldown(userId) {
        const lastMessage = this.cooldowns.get(userId);
        if (!lastMessage) return false;
        
        return Date.now() - lastMessage < this.cooldownTime;
    }

    /**
     * Set cooldown for user
     * @param {string} userId - Discord user ID
     */
    setCooldown(userId) {
        this.cooldowns.set(userId, Date.now());
        
        // Clean up old cooldowns
        setTimeout(() => {
            this.cooldowns.delete(userId);
        }, this.cooldownTime);
    }

    /**
     * Send a system message from Discord to Minecraft
     * @param {string} message - System message
     * @param {string} source - Source of the message (e.g., 'Bot', 'Admin')
     */
    async sendSystemMessage(message, source = 'Discord') {
        try {
            const minecraftMessage = `§7[§b${source}§7] §e${message}`;
            await rconHandler.executeCommand(`say ${minecraftMessage}`);
            console.log(`📤 System → Minecraft: ${message}`);
        } catch (error) {
            console.error('❌ Error sending system message to Minecraft:', error);
        }
    }

    /**
     * Send a private message from Discord to a Minecraft player
     * @param {string} playerName - Target Minecraft player
     * @param {string} message - Message to send
     * @param {string} senderName - Name of the Discord sender
     */
    async sendPrivateMessage(playerName, message, senderName) {
        try {
            const cleanMessage = this.cleanMessage(message);
            const minecraftMessage = `§7[§bDiscord PM§7] §f${senderName}: ${cleanMessage}`;
            
            await rconHandler.tellPlayer(playerName, minecraftMessage);
            console.log(`📤 Discord PM → ${playerName}: ${senderName}: ${cleanMessage}`);
        } catch (error) {
            console.error(`❌ Error sending private message to ${playerName}:`, error);
        }
    }

    /**
     * Announce a Discord event to Minecraft
     * @param {string} event - Event type (e.g., 'user_joined', 'user_left', 'boost')
     * @param {Object} data - Event data
     */
    async announceDiscordEvent(event, data) {
        try {
            let message = '';
            
            switch (event) {
                case 'user_joined':
                    message = `§a${data.username} joined the Discord server!`;
                    break;
                case 'user_left':
                    message = `§c${data.username} left the Discord server.`;
                    break;
                case 'boost':
                    message = `§d${data.username} boosted the Discord server! 🚀`;
                    break;
                case 'level_up':
                    message = `§6${data.username} reached level ${data.level} on Discord!`;
                    break;
                default:
                    message = `§7Discord event: ${event}`;
            }
            
            await this.sendSystemMessage(message, 'Discord');
        } catch (error) {
            console.error(`❌ Error announcing Discord event ${event}:`, error);
        }
    }

    /**
     * Get bridge statistics
     * @returns {Object} - Bridge statistics
     */
    getStats() {
        return {
            activeCooldowns: this.cooldowns.size,
            maxMessageLength: this.maxMessageLength,
            cooldownTime: this.cooldownTime
        };
    }

    /**
     * Clear all cooldowns (admin function)
     */
    clearCooldowns() {
        this.cooldowns.clear();
        console.log('✅ Cleared all chat bridge cooldowns');
    }
}

module.exports = new MinecraftChatBridge();
